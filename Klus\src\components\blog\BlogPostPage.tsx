/**
 * Individual blog post page component with dynamic content fetched from Supabase database.
 * Displays rich HTML content with proper SEO optimization, author information, and responsive design.
 */
import React, { useState, useEffect } from "react";
import { usePara<PERSON>, Link } from "react-router-dom";
import {
  Calendar,
  Clock,
  ArrowLeft,
  Share2,
  User,
  MapPin,
  Tag,
} from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

interface Author {
  id: number;
  name: string;
  avatar_url?: string;
  bio?: string;
  linkedin_url?: string;
}

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  summary: string;
  content: string;
  category: string;
  author_id: number;
  cover_image_url?: string;
  tags: string[];
  city?: string;
  region?: string;
  seo_title?: string;
  seo_description?: string;
  published: boolean;
  published_at: string;
  created_at: string;
  updated_at: string;
  author: Author;
}

export const BlogPostPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const [post, setPost] = useState<BlogPost | null>(null);
  const [relatedPosts, setRelatedPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (slug) {
      fetchPost();
    }
  }, [slug]);

  useEffect(() => {
    if (post) {
      fetchRelatedPosts();
      updatePageMeta();
    }
  }, [post]);

  const fetchPost = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from("68657d51af3489125b35b691_posts")
        .select(
          `
          *,
          author:68657d51af3489125b35b691_authors(name, avatar_url, bio, linkedin_url)
        `
        )
        .eq("slug", slug)
        .eq("published", true)
        .single();

      if (error) {
        console.error("Error fetching post:", error);
        setError("Artikel niet gevonden");
        return;
      }

      setPost(data);
    } catch (error) {
      console.error("Error:", error);
      setError("Er is een fout opgetreden bij het laden van het artikel");
    } finally {
      setLoading(false);
    }
  };

  const fetchRelatedPosts = async () => {
    if (!post) return;

    try {
      const { data, error } = await supabase
        .from("68657d51af3489125b35b691_posts")
        .select(
          `
          *,
          author:68657d51af3489125b35b691_authors(name, avatar_url, bio, linkedin_url)
        `
        )
        .eq("published", true)
        .eq("category", post.category)
        .neq("id", post.id)
        .order("published_at", { ascending: false })
        .limit(3);

      if (error) {
        console.error("Error fetching related posts:", error);
        return;
      }

      setRelatedPosts(data || []);
    } catch (error) {
      console.error("Error:", error);
    }
  };

  const updatePageMeta = () => {
    if (!post) return;

    // Update page title
    document.title = post.seo_title || post.title;

    // Update meta description
    const metaDescription = document.querySelector('meta[name="description"]');
    if (metaDescription) {
      metaDescription.setAttribute(
        "content",
        post.seo_description || post.summary
      );
    }

    // Update Open Graph tags
    const ogTitle = document.querySelector('meta[property="og:title"]');
    if (ogTitle) {
      ogTitle.setAttribute("content", post.title);
    }

    const ogDescription = document.querySelector(
      'meta[property="og:description"]'
    );
    if (ogDescription) {
      ogDescription.setAttribute("content", post.summary);
    }

    const ogImage = document.querySelector('meta[property="og:image"]');
    if (ogImage && post.cover_image_url) {
      ogImage.setAttribute("content", post.cover_image_url);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("nl-NL", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getReadingTime = (content: string) => {
    const wordsPerMinute = 200;
    const words = content.replace(/<[^>]*>/g, "").split(/\s+/).length;
    const minutes = Math.ceil(words / wordsPerMinute);
    return `${minutes} min leestijd`;
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: post?.title,
          text: post?.summary,
          url: window.location.href,
        });
      } catch (error) {
        console.log("Error sharing:", error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      // You could show a toast here
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <div className="container mx-auto px-4 py-8">
          <div className="max-w-4xl mx-auto">
            <div className="h-8 bg-gray-200 rounded animate-pulse mb-4"></div>
            <div className="h-12 bg-gray-200 rounded animate-pulse mb-6"></div>
            <div className="h-64 bg-gray-200 rounded animate-pulse mb-8"></div>
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className="h-4 bg-gray-200 rounded animate-pulse"
                ></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            {error || "Artikel niet gevonden"}
          </h1>
          <Link to="/weblogs">
            <Button>
              <ArrowLeft size={16} className="mr-2" />
              Terug naar blog
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Back button */}
          <Link
            to="/weblogs"
            className="inline-flex items-center text-blue-600 hover:text-blue-800 mb-6"
          >
            <ArrowLeft size={16} className="mr-2" />
            Terug naar blog
          </Link>

          {/* Article header */}
          <header className="mb-8">
            <div className="flex items-center gap-2 mb-4">
              <Badge variant="secondary">{post.category}</Badge>
              {post.city && (
                <div className="flex items-center text-sm text-gray-500">
                  <MapPin size={14} className="mr-1" />
                  {post.city}
                </div>
              )}
            </div>

            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4 leading-tight">
              {post.title}
            </h1>

            <p className="text-xl text-gray-600 mb-6 leading-relaxed">
              {post.summary}
            </p>

            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-500 mb-6">
              <div className="flex items-center">
                <Calendar size={16} className="mr-2" />
                {formatDate(post.published_at)}
              </div>
              <div className="flex items-center">
                <Clock size={16} className="mr-2" />
                {getReadingTime(post.content)}
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleShare}
                className="flex items-center"
              >
                <Share2 size={14} className="mr-2" />
                Delen
              </Button>
            </div>

            {post.cover_image_url && (
              <div className="mb-8">
                <img
                  src={post.cover_image_url}
                  alt={post.title}
                  className="w-full h-64 md:h-96 object-cover rounded-lg shadow-lg"
                />
              </div>
            )}
          </header>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
            {/* Main content */}
            <article className="lg:col-span-3">
              <div
                className="prose prose-lg max-w-none prose-headings:text-gray-900 prose-p:text-gray-700 prose-a:text-blue-600 prose-strong:text-gray-900"
                dangerouslySetInnerHTML={{ __html: post.content }}
              />

              {/* Tags */}
              {post.tags.length > 0 && (
                <div className="mt-8 pt-8 border-t border-gray-200">
                  <div className="flex items-center gap-2 mb-3">
                    <Tag size={16} className="text-gray-500" />
                    <span className="font-medium text-gray-700">Tags:</span>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {post.tags.map((tag, index) => (
                      <Badge key={index} variant="outline">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </article>

            {/* Sidebar */}
            <aside className="lg:col-span-1">
              {/* Author info */}
              <Card className="mb-6">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3 mb-4">
                    <User size={16} className="text-gray-500" />
                    <span className="font-medium text-gray-700">Auteur</span>
                  </div>

                  <div className="flex items-center gap-3">
                    {post.author.avatar_url && (
                      <img
                        src={post.author.avatar_url}
                        alt={post.author.name}
                        className="w-12 h-12 rounded-full"
                      />
                    )}
                    <div>
                      <h3 className="font-medium text-gray-900">
                        {post.author.name}
                      </h3>
                      {post.author.bio && (
                        <p className="text-sm text-gray-600 mt-1">
                          {post.author.bio}
                        </p>
                      )}
                      {post.author.linkedin_url && (
                        <a
                          href={post.author.linkedin_url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-blue-600 text-sm hover:underline mt-2 inline-block"
                        >
                          LinkedIn profiel
                        </a>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Related posts */}
              {relatedPosts.length > 0 && (
                <Card>
                  <CardContent className="p-6">
                    <h3 className="font-medium text-gray-900 mb-4">
                      Gerelateerde artikelen
                    </h3>
                    <div className="space-y-4">
                      {relatedPosts.map((relatedPost, index) => (
                        <div key={relatedPost.id}>
                          <Link
                            to={`/weblogs/${relatedPost.slug}`}
                            className="block hover:text-blue-600 transition-colors"
                          >
                            <h4 className="font-medium text-sm line-clamp-2 mb-1">
                              {relatedPost.title}
                            </h4>
                            <p className="text-xs text-gray-500">
                              {formatDate(relatedPost.published_at)}
                            </p>
                          </Link>
                          {index !== relatedPosts.length - 1 && (
                            <Separator className="mt-4" />
                          )}
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </aside>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BlogPostPage;
