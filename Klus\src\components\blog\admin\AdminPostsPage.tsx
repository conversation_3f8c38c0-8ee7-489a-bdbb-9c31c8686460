/**
 * <PERSON><PERSON> posts management page for the blog system.
 * Allows admins to view, create, edit, and delete blog posts.
 */
import React, { useState, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Search, 
  Filter,
  Eye,
  Calendar,
  User
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { toast } from '@/components/ui/use-toast';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  Alert<PERSON>ialogHeader,
  <PERSON><PERSON><PERSON>ial<PERSON><PERSON><PERSON><PERSON>,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';

interface Author {
  id: number;
  name: string;
  avatar_url?: string;
}

interface BlogPost {
  id: number;
  title: string;
  slug: string;
  summary: string;
  category: string;
  author_id: number;
  cover_image_url?: string;
  tags: string[];
  published: boolean;
  published_at: string | null;
  created_at: string;
  updated_at: string;
  author: Author;
}

export const AdminPostsPage: React.FC = () => {
  const navigate = useNavigate();
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');

  const categories = [
    'SEO',
    'Marketing', 
    'Technologie',
    'Zakelijk',
    'Webdesign',
    'E-commerce',
    'Sociale Media',
    'Content Strategie'
  ];

  useEffect(() => {
    fetchPosts();
  }, []);

  const fetchPosts = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('68657d51af3489125b35b691_posts')
        .select(`
          *,
          author:68657d51af3489125b35b691_authors(id, name, avatar_url)
        `)
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching posts:', error);
        toast({
          title: "Fout bij laden",
          description: "Er is een fout opgetreden bij het laden van de artikelen.",
          variant: "destructive"
        });
        return;
      }

      setPosts(data || []);
    } catch (error) {
      console.error('Error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeletePost = async (postId: number) => {
    try {
      const { error } = await supabase
        .from('68657d51af3489125b35b691_posts')
        .delete()
        .eq('id', postId);

      if (error) {
        throw error;
      }

      toast({
        title: "Artikel verwijderd",
        description: "Het artikel is succesvol verwijderd.",
      });

      // Refresh the posts list
      fetchPosts();
    } catch (error: any) {
      console.error('Error deleting post:', error);
      toast({
        title: "Fout bij verwijderen",
        description: error.message || "Er is een fout opgetreden bij het verwijderen van het artikel.",
        variant: "destructive"
      });
    }
  };

  const filteredPosts = posts.filter(post => {
    const matchesSearch = !searchTerm || 
      post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      post.summary.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesCategory = !selectedCategory || post.category === selectedCategory;
    
    const matchesStatus = !selectedStatus || 
      (selectedStatus === 'published' && post.published) ||
      (selectedStatus === 'draft' && !post.published);
    
    return matchesSearch && matchesCategory && matchesStatus;
  });

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('nl-NL', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getStatusBadge = (post: BlogPost) => {
    if (post.published) {
      return <Badge variant="default" className="bg-green-500">Gepubliceerd</Badge>;
    } else {
      return <Badge variant="secondary">Concept</Badge>;
    }
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Blog Artikelen</h1>
          <p className="text-gray-600">Beheer alle blog artikelen</p>
        </div>
        <Button onClick={() => navigate('/beheerder/blog/posts/new')}>
          <Plus size={16} className="mr-2" />
          Nieuw artikel
        </Button>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardContent className="pt-6">
          <div className="flex flex-wrap gap-4 items-center">
            <div className="flex-1 min-w-[200px]">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <Input
                  placeholder="Zoek artikelen..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Alle categorieën</option>
              {categories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
            
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Alle statussen</option>
              <option value="published">Gepubliceerd</option>
              <option value="draft">Concept</option>
            </select>
            
            {(selectedCategory || selectedStatus || searchTerm) && (
              <Button
                variant="outline"
                onClick={() => {
                  setSelectedCategory('');
                  setSelectedStatus('');
                  setSearchTerm('');
                }}
              >
                Filters wissen
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Posts table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Artikelen ({filteredPosts.length})</span>
            <div className="flex items-center gap-2 text-sm text-gray-500">
              <Filter size={16} />
              {filteredPosts.length} van {posts.length} artikelen
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="space-y-4">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-200 rounded animate-pulse"></div>
              ))}
            </div>
          ) : filteredPosts.length === 0 ? (
            <div className="text-center py-8">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Geen artikelen gevonden
              </h3>
              <p className="text-gray-500 mb-4">
                {posts.length === 0 
                  ? "Er zijn nog geen artikelen aangemaakt."
                  : "Probeer je zoekopdracht aan te passen of de filters te wijzigen."
                }
              </p>
              <Button onClick={() => navigate('/beheerder/blog/posts/new')}>
                <Plus size={16} className="mr-2" />
                Eerste artikel aanmaken
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Titel</TableHead>
                    <TableHead>Categorie</TableHead>
                    <TableHead>Auteur</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Datum</TableHead>
                    <TableHead className="text-right">Acties</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredPosts.map((post) => (
                    <TableRow key={post.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{post.title}</div>
                          <div className="text-sm text-gray-500 line-clamp-1">
                            {post.summary}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{post.category}</Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          {post.author.avatar_url && (
                            <img
                              src={post.author.avatar_url}
                              alt={post.author.name}
                              className="w-6 h-6 rounded-full"
                            />
                          )}
                          <span className="text-sm">{post.author.name}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getStatusBadge(post)}
                      </TableCell>
                      <TableCell>
                        <div className="text-sm">
                          <div>{formatDate(post.created_at)}</div>
                          {post.published_at && (
                            <div className="text-gray-500">
                              Gepubliceerd: {formatDate(post.published_at)}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          {post.published && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(`/weblogs/${post.slug}`, '_blank')}
                            >
                              <Eye size={14} />
                            </Button>
                          )}
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigate(`/beheerder/blog/posts/edit/${post.id}`)}
                          >
                            <Edit size={14} />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button variant="ghost" size="sm" className="text-red-600 hover:text-red-800">
                                <Trash2 size={14} />
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>Artikel verwijderen</AlertDialogTitle>
                                <AlertDialogDescription>
                                  Weet je zeker dat je "{post.title}" wilt verwijderen? 
                                  Deze actie kan niet ongedaan worden gemaakt.
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>Annuleren</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleDeletePost(post.id)}
                                  className="bg-red-600 hover:bg-red-700"
                                >
                                  Verwijderen
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminPostsPage;
